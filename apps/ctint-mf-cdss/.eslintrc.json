{
  "extends": [
    "plugin:@nx/react-typescript",
    "next",
    "next/core-web-vitals",
    "../../.eslintrc.json"
  ],
  "ignorePatterns": ["!**/*", ".next/**/*"],
  "overrides": [
    {
      "files": ["*.ts", "*.tsx", "*.js", "*.jsx"],
      "rules": {
        "@next/next/no-html-link-for-pages": ["error", "apps/ctint-mf-cdss/pages"]
      }
    },
    {
      "files": ["*.ts", "*.tsx"],
      "rules": {"@nx/enforce-module-boundaries": "off"}
    },
    {
      "files": ["*.js", "*.jsx"],
      "rules": {}
    },
    {
      "files": ["*.spec.ts", "*.spec.tsx", "*.spec.js", "*.spec.jsx"],
      "env": {
        "jest": true
      }
    },
    {
      "files": [
        "components/content-creation/**/*.ts", 
        "components/content-creation/**/*.tsx", 
        "components/_ui/content-creation/**/*.ts", 
        "components/_ui/content-creation/**/*.tsx",
        "lib/content-creation/**/*.ts",
        "lib/content-creation/**/*.tsx",
        "i18n/locales/content-creation/**/*.json"
      ],
      "rules": {
        // 为迁移的内容创建组件添加特定规则
        "react/jsx-no-useless-fragment": "off",
        "react/display-name": "off",
        "@typescript-eslint/no-explicit-any": "warn",
        "@typescript-eslint/no-unused-vars": "warn",
        "unused-imports/no-unused-imports": "warn"
      }
    }
  ]
}
